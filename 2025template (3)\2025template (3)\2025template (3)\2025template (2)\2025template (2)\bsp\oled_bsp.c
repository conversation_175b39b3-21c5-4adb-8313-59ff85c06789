#include "oled_bsp.h"
#include "key_bsp.h"  // 包含key_bsp.h以访问task和sure变量
#include "oled.h"
#include "gray_bsp.h"  // 包含循迹模块相关声明
#include "bno08x_bsp.h"  // 包含BNO08X传感器相关声明
#include <stdarg.h>   // 包含va_list等可变参数相关定义
#include <stdio.h>    // 包含vsnprintf函数

// 全局变量：OLED显示的数字
uint32_t oled_display_number = 1;

// ���� OLED ����Ϊ 128 ���أ�ʹ�� 6x8 ����
// ÿ�� 8 ���ظߣ���� 64/8 = 8 �� (y=0~7) �� 32/8 = 4 �� (y=0~3)
// ÿ�� 6 ���ؿ������ 128/6 = 21 ���ַ� (x=0~20? ��������ܻ�������λ��)
// **ע��:** Oled_Printf �� x, y ������λ��Ҫ�ο� OLED_ShowStr ʵ�֣��������ַ�λ�û�����λ��
// �ĵ��е�ע�� (0-127, 0-3) ��ʾ������ 128x32 ��Ļ������ x ������ַ��� y ����

/**
 * @brief	ʹ������printf�ķ�ʽ��ʾ�ַ�������ʾ6x8��С��ASCII�ַ�
 * @param x  ��ʼ X ���� (����) �� �ַ���λ�� (��Ҫ�� OLED_ShowStr)
 * @param y  ��ʼ Y ���� (����) �� �ַ���λ�� (��Ҫ�� OLED_ShowStr, 0-3 �� 0-7)
 * @param format, ... ��ʽ���ַ���������
 * ���磺Oled_Printf(0, 0, "Data = %d", dat);
**/
int Oled_Printf(uint8_t x, uint8_t y, const char *format, ...)
{
  char buffer[512]; // 临时存储格式化后的字符串
  va_list arg;      // 处理可变参数
  int len;          // 最终字符串长度

  va_start(arg, format);
  // 安全地格式化字符串到 buffer
  len = vsnprintf(buffer, sizeof(buffer), format, arg);
  va_end(arg);

  OLED_ShowStr(x, y, buffer, 8);
  return len;
}



void oled_proc(void)
{
	// 清屏
	OLED_Clear();

	// 显示task和sure变量 (第0行和第1行)
	Oled_Printf(0, 0, "task: %d", task);
	Oled_Printf(0, 1, "sure: %d", sure);

	// 显示BNO08X传感器数据 (第2行和第3行)
	if (bno08x_is_yaw_data_valid()) {
		// 显示偏航角(Y轴)，保留2位小数
		Oled_Printf(0, 2, "Yaw: %.2f deg", bno08x_get_yaw_degree());
	} else {
		// 数据无效时显示错误信息
		Oled_Printf(0, 2, "Yaw: ---.-- deg");
	}

	if (bno08x_is_angular_velocity_data_valid()) {
		// 显示角速度(W轴)，保留3位小数
		Oled_Printf(0, 3, "W: %.3f dps", bno08x_get_angular_velocity_dps());
	} else {
		// 数据无效时显示错误信息
		Oled_Printf(0, 3, "W: ---.--- dps");
	}
}
