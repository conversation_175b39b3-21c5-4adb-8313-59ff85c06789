#ifndef __BNO08X_BSP_H__
#define __BNO08X_BSP_H__

#include "bsp_system.h"
#include "i2c.h"

/* BNO08X I2C Address */
#define BNO08X_I2C_ADDR_DEFAULT    0x4A
#define BNO08X_I2C_ADDR_ALT        0x4B

/* BNO08X Hardware Reset Timing */
#define BNO08X_RESET_DELAY_MS      20

/* BNO08X Data Precision Definitions */
#define BNO08X_YAW_PRECISION       2    // 偏航角显示精度(小数点后位数)
#define BNO08X_ANGULAR_VEL_PRECISION 3  // 角速度显示精度(小数点后位数)

/* BNO08X Status Definitions */
typedef enum {
    BNO08X_OK = 0,
    BNO08X_ERROR,
    BNO08X_TIMEOUT,
    BNO08X_BUSY
} BNO08X_Status_t;

/* BNO08X Y轴(偏航角)数据结构 */
typedef struct {
    float raw_value;        // 原始偏航角值(弧度)
    float filtered_value;   // 滤波后的偏航角值(弧度)
    float degree_value;     // 角度值(度)
    uint8_t data_valid;     // 数据有效标志
    uint32_t timestamp;     // 数据时间戳
} BNO08X_Yaw_Data_t;

/* BNO08X W轴(角速度)数据结构 */
typedef struct {
    float raw_value;        // 原始角速度值(rad/s)
    float filtered_value;   // 滤波后的角速度值(rad/s)
    float dps_value;        // 角速度值(度/秒)
    uint8_t data_valid;     // 数据有效标志
    uint32_t timestamp;     // 数据时间戳
} BNO08X_AngularVel_Data_t;

/* BNO08X Data Ready Flag */
extern volatile uint8_t bno08x_data_ready;

/* BNO08X Global Data Variables */
extern BNO08X_Yaw_Data_t bno08x_yaw_data;
extern BNO08X_AngularVel_Data_t bno08x_angular_vel_data;

/* Hardware Abstraction Layer Functions */
void bno08x_bsp_init(void);
BNO08X_Status_t bno08x_bsp_reset(void);
BNO08X_Status_t bno08x_bsp_i2c_write(uint8_t addr, uint8_t *data, uint16_t len);
BNO08X_Status_t bno08x_bsp_i2c_read(uint8_t addr, uint8_t *data, uint16_t len);
uint8_t bno08x_bsp_get_int_status(void);
void bno08x_bsp_delay_ms(uint32_t ms);

/* High-level Interface Functions */
void bno08x_init(void);
void bno08x_proc(void);
BNO08X_Status_t bno08x_check_device(void);

/* BNO08X Data Processing Functions */
BNO08X_Status_t bno08x_read_yaw_data(void);
BNO08X_Status_t bno08x_read_angular_velocity_data(void);
void bno08x_filter_yaw_data(float new_value);
void bno08x_filter_angular_velocity_data(float new_value);
float bno08x_rad_to_degree(float rad_value);
float bno08x_rad_per_sec_to_dps(float rad_per_sec);

/* BNO08X Data Access Functions */
float bno08x_get_yaw_degree(void);
float bno08x_get_angular_velocity_dps(void);
uint8_t bno08x_is_yaw_data_valid(void);
uint8_t bno08x_is_angular_velocity_data_valid(void);

#endif /* __BNO08X_BSP_H__ */
