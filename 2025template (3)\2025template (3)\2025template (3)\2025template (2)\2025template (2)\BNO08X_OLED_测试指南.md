# BNO08X OLED显示系统测试指南

## 📋 功能概述

本项目成功集成了BNO08X传感器的Y轴(偏航角)和W轴(角速度)数据到OLED显示系统中。

### 🎯 实现的功能
- ✅ BNO08X传感器Y轴(偏航角)数据读取
- ✅ BNO08X传感器W轴(角速度)数据读取  
- ✅ 数据滤波处理(低通滤波)
- ✅ 单位转换(弧度→角度，rad/s→dps)
- ✅ OLED实时显示
- ✅ 数据有效性检查

## 🖥️ OLED显示布局

```
第0行: task: [数值]
第1行: sure: [数值]  
第2行: Yaw: [XXX.XX] deg    (偏航角，2位小数)
第3行: W: [XXX.XXX] dps     (角速度，3位小数)
```

## ⚙️ 系统配置

### 数据更新频率
- BNO08X数据读取: 50ms (20Hz)
- OLED显示刷新: 100ms (10Hz)
- 数据滤波系数: 0.8 (可调整)

### 显示精度
- 偏航角: 2位小数 (±XXX.XX度)
- 角速度: 3位小数 (±XXX.XXX度/秒)

## 🧪 测试步骤

### 1. 基础功能测试
1. **编译并下载程序**
   - 确保所有文件正确编译
   - 检查I2C配置(I2C1用于BNO08X，I2C2用于OLED)

2. **OLED显示测试**
   - 上电后检查OLED是否正常显示
   - 确认显示格式正确
   - 检查字符是否清晰

3. **BNO08X连接测试**
   - 检查传感器是否正确连接
   - 观察数据有效性指示
   - 无效数据时应显示"---"

### 2. 数据准确性测试
1. **偏航角测试**
   - 保持传感器水平
   - 缓慢旋转传感器
   - 观察偏航角变化是否合理

2. **角速度测试**
   - 快速旋转传感器
   - 观察角速度数值变化
   - 停止旋转时数值应趋于0

### 3. 滤波效果测试
1. **稳定性测试**
   - 传感器静止时观察数值稳定性
   - 数值应相对稳定，无大幅跳动

2. **响应性测试**
   - 快速改变传感器姿态
   - 观察数据响应速度
   - 滤波后数据应平滑但不失响应性

## 🔧 调试指南

### 常见问题及解决方案

#### 1. OLED显示异常
- **现象**: 屏幕无显示或显示乱码
- **检查**: I2C2连接，OLED电源，初始化代码
- **解决**: 检查硬件连接，确认I2C地址(0x78)

#### 2. BNO08X数据无效
- **现象**: 显示"---"或数据不变
- **检查**: I2C1连接，传感器电源，复位引脚
- **解决**: 检查硬件连接，确认I2C地址(0x4A/0x4B)

#### 3. 数据跳动严重
- **现象**: 显示数值频繁大幅变化
- **调整**: 增大滤波系数(FILTER_ALPHA)
- **位置**: bno08x_bsp.c第19行

#### 4. 数据响应慢
- **现象**: 传感器动作后数据变化延迟
- **调整**: 减小滤波系数或增加读取频率
- **位置**: schedule.c第28行(bno08x_data_proc频率)

### 参数调整

#### 滤波系数调整
```c
// 在bno08x_bsp.c中修改
#define FILTER_ALPHA  0.8f  // 0.0-1.0，越大响应越快
```

#### 显示精度调整
```c
// 在bno08x_bsp.h中修改
#define BNO08X_YAW_PRECISION       2    // 偏航角小数位数
#define BNO08X_ANGULAR_VEL_PRECISION 3  // 角速度小数位数
```

#### 更新频率调整
```c
// 在schedule.c中修改
{bno08x_data_proc,50,0}, // 数据读取频率(ms)
{oled_proc,100,0},       // 显示刷新频率(ms)
```

## 📊 性能指标

### 预期性能
- 数据更新延迟: <50ms
- 显示刷新延迟: <100ms  
- 数据稳定性: ±0.1度(静止时)
- 角速度精度: ±0.01dps

### 资源占用
- RAM增加: ~100字节(数据结构)
- Flash增加: ~2KB(代码)
- CPU占用: <5%(额外)

## 🚀 优化建议

### 性能优化
1. 根据实际需求调整更新频率
2. 优化滤波算法(可考虑卡尔曼滤波)
3. 添加数据校准功能

### 功能扩展
1. 添加数据记录功能
2. 实现数据阈值报警
3. 支持多轴数据显示

### 用户体验
1. 添加单位切换功能
2. 实现显示模式切换
3. 添加数据清零功能

## 📝 注意事项

1. **硬件连接**: 确保I2C上拉电阻正确
2. **电源稳定**: 传感器需要稳定的3.3V供电
3. **环境干扰**: 避免强磁场干扰
4. **温度影响**: 注意温度对传感器精度的影响
5. **校准需求**: 长期使用可能需要重新校准

---

**测试完成标志**: 
- [ ] OLED正常显示所有信息
- [ ] 偏航角数据合理变化
- [ ] 角速度数据响应正常
- [ ] 数据滤波效果良好
- [ ] 系统运行稳定
