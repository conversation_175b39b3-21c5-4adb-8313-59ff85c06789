#include "bno08x_bsp.h"
#include "main.h"

/* Global Variables */
volatile uint8_t bno08x_data_ready = 0;

/* BNO08X Data Storage */
BNO08X_Yaw_Data_t bno08x_yaw_data = {0};
BNO08X_AngularVel_Data_t bno08x_angular_vel_data = {0};

/* BNO08X Register Definitions (simplified for demonstration) */
#define BNO08X_REG_YAW_LSB         0x1A
#define BNO08X_REG_YAW_MSB         0x1B
#define BNO08X_REG_ANGULAR_VEL_X   0x12
#define BNO08X_REG_ANGULAR_VEL_Y   0x14
#define BNO08X_REG_ANGULAR_VEL_Z   0x16

/* Filter coefficient for simple low-pass filter */
#define FILTER_ALPHA               0.8f

/**
 * @brief  Initialize BNO08X BSP layer
 * @param  None
 * @retval None
 */
void bno08x_bsp_init(void)
{
    /* Reset BNO08X sensor */
    bno08x_bsp_reset();
    
    /* Clear data ready flag */
    bno08x_data_ready = 0;
}

/**
 * @brief  Hardware reset BNO08X sensor
 * @param  None
 * @retval BNO08X_Status_t
 */
BNO08X_Status_t bno08x_bsp_reset(void)
{
    /* Pull reset pin low */
    HAL_GPIO_WritePin(BNO_RST_GPIO_Port, BNO_RST_Pin, GPIO_PIN_RESET);
    
    /* Wait for reset timing */
    bno08x_bsp_delay_ms(BNO08X_RESET_DELAY_MS);
    
    /* Release reset pin */
    HAL_GPIO_WritePin(BNO_RST_GPIO_Port, BNO_RST_Pin, GPIO_PIN_SET);
    
    /* Wait for sensor to boot up */
    bno08x_bsp_delay_ms(100);
    
    return BNO08X_OK;
}

/**
 * @brief  I2C write function for BNO08X
 * @param  addr: I2C device address
 * @param  data: Data buffer to write
 * @param  len: Data length
 * @retval BNO08X_Status_t
 */
BNO08X_Status_t bno08x_bsp_i2c_write(uint8_t addr, uint8_t *data, uint16_t len)
{
    HAL_StatusTypeDef status;
    
    status = HAL_I2C_Master_Transmit(&hi2c1, addr << 1, data, len, 1000);
    
    if (status == HAL_OK) {
        return BNO08X_OK;
    } else if (status == HAL_TIMEOUT) {
        return BNO08X_TIMEOUT;
    } else if (status == HAL_BUSY) {
        return BNO08X_BUSY;
    } else {
        return BNO08X_ERROR;
    }
}

/**
 * @brief  I2C read function for BNO08X
 * @param  addr: I2C device address
 * @param  data: Data buffer to read
 * @param  len: Data length
 * @retval BNO08X_Status_t
 */
BNO08X_Status_t bno08x_bsp_i2c_read(uint8_t addr, uint8_t *data, uint16_t len)
{
    HAL_StatusTypeDef status;
    
    status = HAL_I2C_Master_Receive(&hi2c1, addr << 1, data, len, 1000);
    
    if (status == HAL_OK) {
        return BNO08X_OK;
    } else if (status == HAL_TIMEOUT) {
        return BNO08X_TIMEOUT;
    } else if (status == HAL_BUSY) {
        return BNO08X_BUSY;
    } else {
        return BNO08X_ERROR;
    }
}

/**
 * @brief  Get interrupt pin status
 * @param  None
 * @retval uint8_t: 1 if interrupt active, 0 if not
 */
uint8_t bno08x_bsp_get_int_status(void)
{
    return (HAL_GPIO_ReadPin(BNO_INT_GPIO_Port, BNO_INT_Pin) == GPIO_PIN_RESET) ? 1 : 0;
}

/**
 * @brief  Delay function in milliseconds
 * @param  ms: Delay time in milliseconds
 * @retval None
 */
void bno08x_bsp_delay_ms(uint32_t ms)
{
    HAL_Delay(ms);
}

/**
 * @brief  Initialize BNO08X sensor (high-level)
 * @param  None
 * @retval None
 */
void bno08x_init(void)
{
    /* Initialize BSP layer */
    bno08x_bsp_init();
    
    /* Check device presence */
    if (bno08x_check_device() == BNO08X_OK) {
        /* Device found, continue with initialization */
        /* TODO: Add BNO08X specific initialization here */
    }
}

/**
 * @brief  Process BNO08X data (high-level)
 * @param  None
 * @retval None
 */
void bno08x_proc(void)
{
    if (bno08x_data_ready) {
        /* Clear data ready flag */
        bno08x_data_ready = 0;

        /* Read and process yaw data */
        bno08x_read_yaw_data();

        /* Read and process angular velocity data */
        bno08x_read_angular_velocity_data();
    }
}

/**
 * @brief  Check if BNO08X device is present
 * @param  None
 * @retval BNO08X_Status_t
 */
BNO08X_Status_t bno08x_check_device(void)
{
    uint8_t test_data = 0;
    BNO08X_Status_t status;

    /* Try to read from device with default address */
    status = bno08x_bsp_i2c_read(BNO08X_I2C_ADDR_DEFAULT, &test_data, 1);

    if (status != BNO08X_OK) {
        /* Try alternative address */
        status = bno08x_bsp_i2c_read(BNO08X_I2C_ADDR_ALT, &test_data, 1);
    }

    return status;
}

/**
 * @brief  Read Y-axis (Yaw) data from BNO08X
 * @param  None
 * @retval BNO08X_Status_t
 */
BNO08X_Status_t bno08x_read_yaw_data(void)
{
    uint8_t data_buffer[4];
    BNO08X_Status_t status;
    int16_t raw_yaw;
    float yaw_rad;

    /* Read yaw data from sensor (simplified implementation) */
    status = bno08x_bsp_i2c_read(BNO08X_I2C_ADDR_DEFAULT, data_buffer, 4);

    if (status == BNO08X_OK) {
        /* Parse raw data (example: combine LSB and MSB) */
        raw_yaw = (int16_t)((data_buffer[1] << 8) | data_buffer[0]);

        /* Convert to radians (scaling factor depends on sensor configuration) */
        yaw_rad = (float)raw_yaw * 0.0001f; // Example scaling factor

        /* Store raw value */
        bno08x_yaw_data.raw_value = yaw_rad;

        /* Apply filtering */
        bno08x_filter_yaw_data(yaw_rad);

        /* Convert to degrees */
        bno08x_yaw_data.degree_value = bno08x_rad_to_degree(bno08x_yaw_data.filtered_value);

        /* Update timestamp and validity */
        bno08x_yaw_data.timestamp = HAL_GetTick();
        bno08x_yaw_data.data_valid = 1;
    } else {
        /* Mark data as invalid on read error */
        bno08x_yaw_data.data_valid = 0;
    }

    return status;
}

/**
 * @brief  Read W-axis (Angular Velocity) data from BNO08X
 * @param  None
 * @retval BNO08X_Status_t
 */
BNO08X_Status_t bno08x_read_angular_velocity_data(void)
{
    uint8_t data_buffer[6];
    BNO08X_Status_t status;
    int16_t raw_angular_vel_z;
    float angular_vel_rad_per_sec;

    /* Read angular velocity data from sensor (Z-axis for yaw rate) */
    status = bno08x_bsp_i2c_read(BNO08X_I2C_ADDR_DEFAULT, data_buffer, 6);

    if (status == BNO08X_OK) {
        /* Parse raw Z-axis angular velocity data */
        raw_angular_vel_z = (int16_t)((data_buffer[5] << 8) | data_buffer[4]);

        /* Convert to rad/s (scaling factor depends on sensor configuration) */
        angular_vel_rad_per_sec = (float)raw_angular_vel_z * 0.001f; // Example scaling factor

        /* Store raw value */
        bno08x_angular_vel_data.raw_value = angular_vel_rad_per_sec;

        /* Apply filtering */
        bno08x_filter_angular_velocity_data(angular_vel_rad_per_sec);

        /* Convert to degrees per second */
        bno08x_angular_vel_data.dps_value = bno08x_rad_per_sec_to_dps(bno08x_angular_vel_data.filtered_value);

        /* Update timestamp and validity */
        bno08x_angular_vel_data.timestamp = HAL_GetTick();
        bno08x_angular_vel_data.data_valid = 1;
    } else {
        /* Mark data as invalid on read error */
        bno08x_angular_vel_data.data_valid = 0;
    }

    return status;
}

/**
 * @brief  Apply low-pass filter to yaw data
 * @param  new_value: New raw yaw value in radians
 * @retval None
 */
void bno08x_filter_yaw_data(float new_value)
{
    static uint8_t first_run = 1;

    if (first_run) {
        /* Initialize filter with first value */
        bno08x_yaw_data.filtered_value = new_value;
        first_run = 0;
    } else {
        /* Apply low-pass filter: filtered = α * new + (1-α) * old */
        bno08x_yaw_data.filtered_value = FILTER_ALPHA * new_value +
                                        (1.0f - FILTER_ALPHA) * bno08x_yaw_data.filtered_value;
    }
}

/**
 * @brief  Apply low-pass filter to angular velocity data
 * @param  new_value: New raw angular velocity value in rad/s
 * @retval None
 */
void bno08x_filter_angular_velocity_data(float new_value)
{
    static uint8_t first_run = 1;

    if (first_run) {
        /* Initialize filter with first value */
        bno08x_angular_vel_data.filtered_value = new_value;
        first_run = 0;
    } else {
        /* Apply low-pass filter: filtered = α * new + (1-α) * old */
        bno08x_angular_vel_data.filtered_value = FILTER_ALPHA * new_value +
                                                (1.0f - FILTER_ALPHA) * bno08x_angular_vel_data.filtered_value;
    }
}

/**
 * @brief  Convert radians to degrees
 * @param  rad_value: Value in radians
 * @retval float: Value in degrees
 */
float bno08x_rad_to_degree(float rad_value)
{
    return rad_value * 180.0f / 3.14159265359f;
}

/**
 * @brief  Convert rad/s to degrees per second
 * @param  rad_per_sec: Value in rad/s
 * @retval float: Value in degrees per second
 */
float bno08x_rad_per_sec_to_dps(float rad_per_sec)
{
    return rad_per_sec * 180.0f / 3.14159265359f;
}

/**
 * @brief  Get filtered yaw angle in degrees
 * @param  None
 * @retval float: Yaw angle in degrees
 */
float bno08x_get_yaw_degree(void)
{
    return bno08x_yaw_data.degree_value;
}

/**
 * @brief  Get filtered angular velocity in degrees per second
 * @param  None
 * @retval float: Angular velocity in degrees per second
 */
float bno08x_get_angular_velocity_dps(void)
{
    return bno08x_angular_vel_data.dps_value;
}

/**
 * @brief  Check if yaw data is valid
 * @param  None
 * @retval uint8_t: 1 if valid, 0 if invalid
 */
uint8_t bno08x_is_yaw_data_valid(void)
{
    return bno08x_yaw_data.data_valid;
}

/**
 * @brief  Check if angular velocity data is valid
 * @param  None
 * @retval uint8_t: 1 if valid, 0 if invalid
 */
uint8_t bno08x_is_angular_velocity_data_valid(void)
{
    return bno08x_angular_vel_data.data_valid;
}
